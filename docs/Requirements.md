# Requirements
- A reasonable host to run as the passthrough, I am using a 
  [Raspberry Pi 3](https://www.raspberrypi.org/products/raspberry-pi-3-model-b/) 
  with good success; however any host you can run OLA and install python 
  should work.
- Any [OLA supported DMX interface](https://www.openlighting.org/ola/get-help/ola-faq/#What_are_the_recommended_USB_Device_to_use_with_OLA) should work.
  I am using a 
  [DMX Hat](http://bitwizard.nl/shop/DMX-interface-for-Raspberry-pi)
  on the Raspi 3 and have used a [DMXKing USB](https://www.amazon.com/DMXking-ultraDMX-Micro-adapter-dongle/dp/B00T8OKM98/ref=sr_1_cc_1?s=aps&ie=UTF8&qid=1523466866&sr=1-1-catcorr&keywords=DMXKing)
- A [USB to Xbox 360 Wireless controller](https://www.amazon.com/Microsoft-Authentic-Wireless-Receiver-Windows/dp/B00FAS1WDG/ref=sr_1_6?ie=UTF8&qid=1523465136&sr=8-6&keywords=USB+Xbox+wireless)
- A Wireless Xbox 360 Controller ( could use a wired .. but havn't had
the need yet )
